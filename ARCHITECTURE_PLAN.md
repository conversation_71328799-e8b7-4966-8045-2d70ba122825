# Next.js 14 Frontend Architecture Refactoring Plan

## 🎯 Target Architecture: "Server-First with Strategic Client Islands"

### Core Design Principles

1. **Server Components by Default**
   - All pages, layouts, and components are Server Components unless they need client-side features
   - Use async/await for server-side data fetching
   - Leverage Next.js 14 App Router capabilities

2. **Strategic Client Islands**
   - Only use "use client" for components that need:
     - Event handlers (onClick, onChange)
     - Browser APIs (localStorage, window)
     - React hooks (useState, useEffect)
     - Third-party client libraries

3. **Colocation with Private Folders**
   - Use `_components` for route-specific components
   - Use `_lib` for route-specific logic
   - Use `_types` for route-specific types

4. **Route Groups for Organization**
   - `(marketing)` for public pages
   - `(dashboard)` for authenticated pages
   - `(auth)` for authentication pages

## 📁 Target Project Structure

```
work-finder-client/
├── src/
│   ├── app/                          # Next.js 14 App Router
│   │   ├── (marketing)/              # Public pages (no URL segment)
│   │   │   ├── page.tsx              # Home page (Server Component)
│   │   │   ├── about/
│   │   │   │   └── page.tsx          # About page (Server Component)
│   │   │   ├── companies/
│   │   │   │   ├── _components/      # Route-specific components
│   │   │   │   ├── _lib/             # Route-specific logic
│   │   │   │   └── page.tsx          # Companies page
│   │   │   └── jobs/
│   │   │       ├── _components/
│   │   │       ├── _lib/
│   │   │       └── page.tsx
│   │   ├── (auth)/                   # Authentication pages
│   │   │   ├── layout.tsx            # Auth layout (Server Component)
│   │   │   ├── login/
│   │   │   │   ├── _components/      # Login form (Client Component)
│   │   │   │   └── page.tsx          # Login page (Server Component)
│   │   │   └── register/
│   │   │       ├── _components/
│   │   │       └── page.tsx
│   │   ├── (dashboard)/              # Protected pages
│   │   │   ├── layout.tsx            # Dashboard layout (Server Component)
│   │   │   ├── _components/          # Shared dashboard components
│   │   │   │   ├── sidebar.tsx       # Client Component (interactive)
│   │   │   │   └── header.tsx        # Server Component (mostly static)
│   │   │   ├── page.tsx              # Dashboard home
│   │   │   ├── applications/
│   │   │   └── saved-jobs/
│   │   ├── layout.tsx                # Root layout (Server Component)
│   │   ├── loading.tsx               # Global loading UI
│   │   ├── error.tsx                 # Global error UI
│   │   ├── not-found.tsx             # 404 page
│   │   └── globals.css               # Global styles
│   ├── components/                   # Shared components
│   │   ├── ui/                       # Shadcn/ui components
│   │   ├── forms/                    # Form components (Client Components)
│   │   ├── sections/                 # Landing page sections (Server Components)
│   │   └── providers/                # React providers (Client Components)
│   │       ├── query-provider.tsx
│   │       ├── theme-provider.tsx
│   │       └── index.ts
│   ├── lib/                          # Shared utilities
│   │   ├── api/                      # API layer (fetch-based)
│   │   │   ├── auth.ts
│   │   │   ├── jobs.ts
│   │   │   ├── companies.ts
│   │   │   ├── utils.ts
│   │   │   └── index.ts
│   │   ├── validations/              # Zod schemas
│   │   ├── query-client.ts           # TanStack Query config
│   │   └── utils.ts                  # Shared utilities
│   ├── hooks/                        # TanStack Query hooks only
│   │   ├── use-jobs.ts
│   │   ├── use-companies.ts
│   │   └── use-applications.ts
│   ├── stores/                       # Zustand stores (minimal usage)
│   │   └── auth-store.ts             # Only for global state
│   ├── types/                        # TypeScript definitions
│   │   ├── api.ts
│   │   ├── auth.ts
│   │   └── index.ts
│   └── constants/                    # App constants
│       ├── routes.ts
│       └── navigation.ts
├── public/                           # Static assets
├── middleware.ts                     # Next.js middleware
├── next.config.ts                    # Next.js configuration
├── tailwind.config.ts                # Tailwind configuration
└── package.json                      # Dependencies
```

## 🗑️ Files to Remove

### Immediate Cleanup
1. **Empty/Legacy Files:**
   - `src/services/ApiService.ts` (empty)
   - `src/pages/Config.ts` (empty)
   - `src/contexts/` (empty directory)

2. **Duplicate API Patterns:**
   - `src/constants/Api.ts` (duplicate)
   - `src/constants/api.ts` (old pattern)

3. **Duplicate Providers:**
   - Consolidate all providers into `src/components/providers/`

## 🔄 Component Conversion Strategy

### Server Components (Default)
- All pages and layouts
- Static content components
- Data fetching components
- SEO-critical components

### Client Components (Explicit "use client")
- Forms with validation
- Interactive UI (dropdowns, modals, buttons with onClick)
- Components using browser APIs
- State management components

## 📊 Data Fetching Patterns

### Server Components
```typescript
// Direct async/await in Server Components
export default async function JobsPage() {
  const jobs = await fetchJobs();
  return <JobsList jobs={jobs} />;
}
```

### Client Components
```typescript
// TanStack Query in Client Components
"use client";
export default function InteractiveJobs() {
  const { data: jobs } = useJobs();
  return <JobsList jobs={jobs} />;
}
```

## 🎨 Styling Strategy
- Tailwind CSS utility classes
- CSS variables for theming
- Responsive design with Tailwind prefixes
- Dark mode support with next-themes

## 🔧 Development Guidelines

### Component Creation Rules
1. Start with Server Component (no "use client")
2. Add "use client" only when needed
3. Use private folders for route-specific code
4. Colocate related files

### State Management Rules
1. Use Zustand only for global state (auth, theme)
2. Use React state for local component state
3. Use TanStack Query for server state
4. Avoid unnecessary state management

### File Naming Conventions
- `page.tsx` for pages
- `layout.tsx` for layouts
- `loading.tsx` for loading states
- `error.tsx` for error boundaries
- `_components/` for private components
- `_lib/` for private utilities

This architecture provides:
- ✅ Better performance (less JavaScript)
- ✅ Improved SEO (more server rendering)
- ✅ Cleaner code organization
- ✅ Easier maintenance
- ✅ Better developer experience
