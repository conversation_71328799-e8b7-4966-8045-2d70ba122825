# Work Finder Frontend - Development Guidelines

## 🏗️ Architecture Overview

This project follows the **"Server-First with Strategic Client Islands"** architecture pattern, optimized for Next.js 14 App Router.

### Core Principles

1. **Server Components by Default** - All components are Server Components unless they need client-side interactivity
2. **Strategic Client Islands** - Only use "use client" when absolutely necessary
3. **Colocation with Private Folders** - Keep related code close using `_components`, `_lib` patterns
4. **Route Groups for Organization** - Use `(marketing)`, `(dashboard)` for logical grouping
5. **Single Data Fetching Pattern** - TanStack Query for client-side, direct async/await for server-side

## 📁 Project Structure

```
src/
├── app/                          # Next.js 14 App Router
│   ├── (marketing)/              # Public pages (no URL segment)
│   │   ├── page.tsx              # Home page (Server Component)
│   │   ├── about/page.tsx        # About page (Server Component)
│   │   ├── companies/page.tsx    # Companies page
│   │   └── jobs/page.tsx         # Jobs page
│   ├── (auth)/                   # Authentication pages
│   │   ├── layout.tsx            # Auth layout (Server Component)
│   │   ├── login/page.tsx        # Login page (Server Component)
│   │   └── register/page.tsx     # Register page (Server Component)
│   ├── (dashboard)/              # Protected pages
│   │   ├── layout.tsx            # Dashboard layout (Server Component)
│   │   ├── _components/          # Dashboard-specific components
│   │   │   ├── dashboard-header.tsx  # Client Component (interactive)
│   │   │   └── dashboard-sidebar.tsx # Client Component (interactive)
│   │   ├── page.tsx              # Dashboard home
│   │   ├── applications/         # Applications pages
│   │   └── saved-jobs/           # Saved jobs pages
│   ├── layout.tsx                # Root layout (Server Component)
│   ├── loading.tsx               # Global loading UI
│   ├── error.tsx                 # Global error UI
│   └── globals.css               # Global styles
├── components/                   # Shared components
│   ├── ui/                       # Shadcn/ui components
│   ├── forms/                    # Form components (Client Components)
│   ├── sections/                 # Landing page sections (Server Components)
│   └── providers/                # React providers (Client Components)
├── lib/                          # Shared utilities
│   ├── api/                      # API layer (fetch-based)
│   ├── validations/              # Zod schemas
│   ├── query-client.ts           # TanStack Query config
│   └── utils.ts                  # Shared utilities
├── hooks/                        # TanStack Query hooks only
├── stores/                       # Zustand stores (minimal usage)
├── types/                        # TypeScript definitions
└── constants/                    # App constants
```

## 🎯 Component Guidelines

### When to Use Server Components (Default)
- All pages and layouts
- Static content components
- Data fetching components
- SEO-critical components
- Components that don't need interactivity

### When to Use Client Components ("use client")
- Forms with validation and state
- Interactive UI (dropdowns, modals, buttons with onClick)
- Components using browser APIs (localStorage, window)
- Components using React hooks (useState, useEffect)
- Third-party client libraries

### Example Patterns

#### ✅ Server Component (Default)
```typescript
// app/about/page.tsx
import { Card } from "@/components/ui/card";

export default function AboutPage() {
  return (
    <div>
      <h1>About Us</h1>
      <Card>Static content here</Card>
    </div>
  );
}
```

#### ✅ Client Component (When Needed)
```typescript
// components/forms/contact-form.tsx
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";

export function ContactForm() {
  const [email, setEmail] = useState("");
  
  return (
    <form>
      <input 
        value={email} 
        onChange={(e) => setEmail(e.target.value)} 
      />
      <Button type="submit">Submit</Button>
    </form>
  );
}
```

#### ✅ Mixed Pattern (Server with Client Islands)
```typescript
// app/dashboard/layout.tsx (Server Component)
import { DashboardHeader } from "./_components/dashboard-header"; // Client
import { DashboardSidebar } from "./_components/dashboard-sidebar"; // Client

export default function DashboardLayout({ children }) {
  return (
    <div>
      <DashboardHeader /> {/* Interactive header */}
      <div className="flex">
        <DashboardSidebar /> {/* Interactive sidebar */}
        <main>{children}</main> {/* Server-rendered content */}
      </div>
    </div>
  );
}
```

## 📊 Data Fetching Patterns

### Server Components (Preferred)
```typescript
// Direct async/await in Server Components
export default async function JobsPage() {
  const jobs = await fetchJobs(); // Server-side fetch
  return <JobsList jobs={jobs} />;
}
```

### Client Components
```typescript
// TanStack Query in Client Components
"use client";
import { useJobs } from "@/hooks/use-jobs";

export default function InteractiveJobs() {
  const { data: jobs, isLoading } = useJobs();
  
  if (isLoading) return <div>Loading...</div>;
  return <JobsList jobs={jobs} />;
}
```

## 🎨 Styling Guidelines

- Use Tailwind CSS utility classes
- Implement responsive design with Tailwind prefixes (sm:, md:, lg:, xl:)
- Use CSS variables for theming
- Support dark mode with next-themes
- Keep custom CSS minimal

## 🔧 State Management Rules

1. **Zustand** - Only for global state (auth, theme, user preferences)
2. **React State** - For local component state (form inputs, UI toggles)
3. **TanStack Query** - For server state (API data, caching)
4. **URL State** - For shareable state (search params, filters)

## 📝 File Naming Conventions

- `page.tsx` - Route pages
- `layout.tsx` - Route layouts
- `loading.tsx` - Loading states
- `error.tsx` - Error boundaries
- `not-found.tsx` - 404 pages
- `_components/` - Private components (not routable)
- `_lib/` - Private utilities (not routable)
- `_types/` - Private types (not routable)

## 🚀 Performance Best Practices

1. **Minimize Client JavaScript** - Use Server Components by default
2. **Strategic Code Splitting** - Use dynamic imports for heavy components
3. **Optimize Images** - Always use Next.js Image component
4. **Implement Proper Caching** - Use appropriate cache strategies
5. **Bundle Analysis** - Regularly check bundle size

## 🔒 Security Guidelines

1. **Environment Variables** - Use NEXT_PUBLIC_ prefix only for client-safe values
2. **API Keys** - Keep sensitive keys server-side only
3. **Input Validation** - Use Zod schemas for all user inputs
4. **Authentication** - Implement proper middleware protection

## 🧪 Testing Strategy

1. **Unit Tests** - For utilities and pure functions
2. **Integration Tests** - For API routes and data fetching
3. **Component Tests** - For complex interactive components
4. **E2E Tests** - For critical user flows

## 📚 Development Workflow

1. **Start with Server Components** - Always default to server-side
2. **Add Client Interactivity** - Only when needed
3. **Colocate Related Code** - Use private folders
4. **Type Everything** - Comprehensive TypeScript usage
5. **Test Early** - Write tests alongside features

This architecture ensures optimal performance, maintainability, and developer experience while leveraging Next.js 14's full capabilities.
