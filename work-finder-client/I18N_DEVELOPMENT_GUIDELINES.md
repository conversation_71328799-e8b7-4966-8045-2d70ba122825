# Work Finder Frontend - i18n Development Guidelines

## 🌐 Internationalization Architecture Overview

The project uses **next-intl without i18n routing**, optimized for Next.js 14 App Router with a server-first approach.

### Key Features
- ✅ No URL-based language routing (same URL for all languages)
- ✅ Server-first translation approach for better performance
- ✅ Optimized language switching without full page reload flash
- ✅ Automatic language detection and persistence
- ✅ Type-safe translation handling
- ✅ Graceful fallbacks for missing translations

## 📁 Project Structure

```
src/
├── i18n/
│   └── request.ts              # next-intl configuration
├── lib/i18n/
│   ├── server-translations.ts  # Server Component utilities
│   ├── language-switcher.ts    # Optimized switching logic
│   ├── language-detection.ts   # Detection and persistence
│   └── types.ts                # TypeScript definitions
├── hooks/
│   └── useTranslation.ts       # Enhanced translation hooks
├── components/ui/
│   ├── language-switcher.tsx   # Language switcher component
│   └── language-loading.tsx    # Loading state handler
└── messages/
    ├── en.json                 # English (default)
    ├── vi.json                 # Vietnamese
    └── [locale].json           # Additional locales
```

## 🎯 Translation Patterns

### Server Components (Preferred)

#### Basic Usage
```typescript
// app/about/page.tsx
import { getTranslations } from 'next-intl/server';

export default async function AboutPage() {
  const t = await getTranslations('about');
  return (
    <div>
      <h1>{t('title')}</h1>
      <p>{t('description')}</p>
    </div>
  );
}
```

#### Using Server Utilities
```typescript
// For complex scenarios
import { 
  getServerNavigationTranslations,
  getServerCommonTranslations,
  formatServerDateTime 
} from '@/lib/i18n/server-translations';

export default async function ServerComponent() {
  const nav = await getServerNavigationTranslations();
  const common = await getServerCommonTranslations();
  const formattedDate = await formatServerDateTime(new Date());
  
  return (
    <div>
      <nav>{nav.home}</nav>
      <button>{common.submit}</button>
      <time>{formattedDate}</time>
    </div>
  );
}
```

#### Metadata Translation
```typescript
// app/about/page.tsx
import { getServerMetadataTranslations } from '@/lib/i18n/server-translations';

export async function generateMetadata() {
  const meta = await getServerMetadataTranslations('about');
  return {
    title: meta.title,
    description: meta.description,
    keywords: meta.keywords
  };
}
```

### Client Components

#### Basic Usage
```typescript
// components/interactive-form.tsx
"use client";
import { useTranslations } from 'next-intl';

export function InteractiveForm() {
  const t = useTranslations('forms');
  
  return (
    <form>
      <input placeholder={t('email')} />
      <button type="submit">{t('submit')}</button>
    </form>
  );
}
```

#### Using Custom Hooks
```typescript
// components/navigation.tsx
"use client";
import { useNavigationTranslation, useCommonTranslation } from '@/hooks/useTranslation';

export function Navigation() {
  const nav = useNavigationTranslation();
  const common = useCommonTranslation();
  
  return (
    <nav>
      <a href="/">{nav.home}</a>
      <a href="/jobs">{nav.jobs}</a>
      <button>{common.search}</button>
    </nav>
  );
}
```

## 🔄 Language Switching

### Optimized Language Switcher
```typescript
// components/language-selector.tsx
"use client";
import { useTranslation } from '@/hooks/useTranslation';
import { LanguageSwitcher } from '@/components/ui/language-switcher';

export function LanguageSelector() {
  const { changeLanguage, locale } = useTranslation();
  
  return (
    <LanguageSwitcher
      currentLocale={locale}
      onLanguageChange={changeLanguage}
      variant="outline"
      showLabel={true}
    />
  );
}
```

### Language Detection Flow
1. **Server-side**: Detects from headers/cookies on initial load
2. **Client-side**: Reads from localStorage for subsequent visits
3. **Fallback**: Uses default locale (English) if detection fails
4. **Persistence**: Saves user preference in localStorage

## 📝 Translation File Organization

### Structure
```json
{
  "navigation": {
    "home": "Home",
    "jobs": "Jobs",
    "companies": "Companies"
  },
  "common": {
    "loading": "Loading...",
    "error": "An error occurred",
    "submit": "Submit"
  },
  "auth": {
    "login": "Login",
    "register": "Register",
    "forgotPassword": "Forgot Password?"
  },
  "dashboard": {
    "overview": "Overview",
    "profile": "Profile",
    "settings": "Settings"
  },
  "metadata": {
    "home": {
      "title": "Work Finder - Find Your Dream Job",
      "description": "Discover job opportunities"
    }
  }
}
```

### Naming Conventions
- Use nested objects for logical grouping
- Use camelCase for keys
- Keep keys descriptive but concise
- Group related translations together

## 🎨 Advanced Patterns

### Conditional Content by Locale
```typescript
import { getServerLocale } from '@/lib/i18n/server-translations';

export default async function LocalizedContent() {
  const locale = await getServerLocale();
  
  return (
    <div>
      {locale === 'vi' ? (
        <VietnamSpecificContent />
      ) : (
        <DefaultContent />
      )}
    </div>
  );
}
```

### Error Message Translation
```typescript
"use client";
import { useTranslation } from '@/hooks/useTranslation';

export function ErrorHandler({ error }: { error: any }) {
  const { translateError } = useTranslation();
  
  return (
    <div className="error">
      {translateError(error)}
    </div>
  );
}
```

### Number and Date Formatting
```typescript
// Server-side
import { formatServerCurrency, formatServerDateTime } from '@/lib/i18n/server-translations';

const price = await formatServerCurrency(1000000, 'VND');
const date = await formatServerDateTime(new Date());

// Client-side
import { useLocale } from 'next-intl';

const locale = useLocale();
const formatter = new Intl.NumberFormat(locale, { style: 'currency', currency: 'VND' });
const formattedPrice = formatter.format(1000000);
```

## 🚀 Performance Best Practices

### 1. Server-First Approach
- Use Server Components for static content
- Leverage server-side translations for better SEO
- Only use Client Components when interactivity is needed

### 2. Optimized Loading
- Translations are loaded per request (not bundled)
- Language switching shows loading state
- Graceful fallbacks prevent broken UI

### 3. Bundle Optimization
- Only active locale messages are loaded
- Server-side rendering reduces client JavaScript
- Efficient caching of translation files

## 🔧 Development Workflow

### Adding New Translations
1. Add key to all locale files (`en.json`, `vi.json`)
2. Use in components with appropriate hook/function
3. Test in both languages
4. Verify fallbacks work

### Adding New Locale
1. Create `messages/[locale].json`
2. Add to `SupportedLocale` type in `types.ts`
3. Update `LANGUAGE_INFO` configuration
4. Test language detection and switching

### Testing Translations
```typescript
// Test missing translations
const t = useTranslations();
console.log(t('nonexistent.key')); // Falls back to key name

// Test locale switching
const { changeLanguage } = useTranslation();
changeLanguage('vi'); // Switches to Vietnamese
```

## 🐛 Troubleshooting

### Common Issues
- **Missing translations**: Keys fallback to key name, check console warnings
- **Server/Client mismatch**: Ensure consistent locale detection
- **Performance issues**: Use Server Components when possible
- **Type errors**: Update `SupportedLocale` type when adding locales

### Debug Mode
```typescript
// Enable debugging
import { useLocale } from 'next-intl';

const locale = useLocale();
console.log('Current locale:', locale);
console.log('Available translations:', Object.keys(messages));
```

This optimized i18n architecture provides excellent performance, developer experience, and maintainability while fully leveraging Next.js 14's server-first capabilities.
