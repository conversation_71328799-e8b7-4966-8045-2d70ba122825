"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  User,
  FileText,
  Bookmark,
  Settings,
  LogOut,
  Bell,
  Home,
  Briefcase,
  Users,
  Building2,
  PlusCircle,
} from "lucide-react";
import { DASHBOARD_ROUTES, PUBLIC_ROUTES } from "@/constants/routes";
import { useAuthStore } from "@/stores/user-store";
import { useTranslation } from "@/hooks/useTranslation";
import { CompactLanguageSwitcher } from "@/components/ui/language-switcher";

// Role-based navigation items
const getNavigationItems = (userRole: string, t: any) => {
  const commonItems = [
    {
      label: t("dashboard.overview"),
      href: DASHBOARD_ROUTES.OVERVIEW,
      icon: Home,
      roles: ["job_seeker", "employer"],
    },
    {
      label: t("dashboard.profile"),
      href: DASHBOARD_ROUTES.PROFILE,
      icon: User,
      roles: ["job_seeker", "employer"],
    },
    {
      label: t("dashboard.settings"),
      href: DASHBOARD_ROUTES.SETTINGS,
      icon: Settings,
      roles: ["job_seeker", "employer"],
    },
  ];

  const jobSeekerItems = [
    {
      label: t("dashboard.myApplications"),
      href: DASHBOARD_ROUTES.APPLICATIONS,
      icon: Briefcase,
      roles: ["job_seeker"],
    },
    {
      label: t("dashboard.savedJobs"),
      href: DASHBOARD_ROUTES.SAVED_JOBS,
      icon: Bookmark,
      roles: ["job_seeker"],
    },
    {
      label: t("dashboard.resume"),
      href: DASHBOARD_ROUTES.RESUME,
      icon: FileText,
      roles: ["job_seeker"],
    },
  ];

  const employerItems = [
    {
      label: "My Jobs",
      href: "/dashboard/jobs",
      icon: Briefcase,
      roles: ["employer"],
    },
    {
      label: "Candidates",
      href: "/dashboard/candidates",
      icon: Users,
      roles: ["employer"],
    },
    {
      label: "Post Job",
      href: "/dashboard/jobs/new",
      icon: PlusCircle,
      roles: ["employer"],
    },
    {
      label: "Company Profile",
      href: "/dashboard/company",
      icon: Building2,
      roles: ["employer"],
    },
  ];

  const allItems = [...commonItems, ...jobSeekerItems, ...employerItems];
  return allItems.filter((item) => item.roles.includes(userRole));
};

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, logout, userDisplayName } = useAuthStore();
  const { t } = useTranslation();

  // Get navigation items based on user role
  const userRole = user?.role || "job_seeker";
  const navigationItems = getNavigationItems(userRole, t);

  const handleLogout = async () => {
    await logout();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link href={PUBLIC_ROUTES.HOME} className="flex items-center">
              <span className="text-xl font-bold text-blue-600">
                Work Finder
              </span>
            </Link>

            {/* Right side */}
            <div className="flex items-center space-x-4">
              {/* Language Switcher */}
              <CompactLanguageSwitcher />

              {/* Notifications */}
              <Button variant="ghost" size="sm">
                <Bell className="h-5 w-5" />
              </Button>

              {/* User Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="relative h-8 w-8 rounded-full"
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user?.avatar} alt="User" />
                      <AvatarFallback>
                        {userDisplayName
                          .split(" ")
                          .map((n) => n[0])
                          .join("")
                          .toUpperCase() || "U"}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {userDisplayName}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user?.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href={DASHBOARD_ROUTES.PROFILE}>
                      <User className="mr-2 h-4 w-4" />
                      <span>{t("dashboard.profile")}</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href={DASHBOARD_ROUTES.SETTINGS}>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>{t("dashboard.settings")}</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>{t("navigation.logout")}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white shadow-sm min-h-screen">
          <nav className="mt-8 px-4">
            <ul className="space-y-2">
              {navigationItems.map((item) => (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 group"
                  >
                    <item.icon className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-8">{children}</main>
      </div>
    </div>
  );
}
