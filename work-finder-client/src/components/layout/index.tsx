// Layout components
export { default as Header } from "./header/header";
export { default as Footer } from "./footer";
export { ConditionalLayout } from "./conditional-layout";

// Page layout components
export { default as PageContainer } from "./page-wrapper";

// Responsive layout components available but not exported to avoid conflicts
// Import directly from "./responsive-layout" if needed

// Header sub-components
export { default as Navigation } from "./header/navigation";
export { default as Logo } from "./header/logo";
export { default as MobileNavigation } from "./header/mobile-navigation";
export { default as UserMenu } from "./header/user-menu";

// Type exports
export type { HeaderProps, FooterProps } from "@/types/layout";
