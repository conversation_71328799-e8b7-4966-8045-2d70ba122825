import { getRequestConfig } from "next-intl/server";
import { routing } from "./routing";
import { headers } from "next/headers";

export default getRequestConfig(async ({ requestLocale }) => {
  // This typically corresponds to the `[locale]` segment
  let locale = await requestLocale;

  // Ensure that a valid locale is used
  if (!locale || !routing.locales.includes(locale as any)) {
    // Fallback to header-based detection or default locale
    const headersList = headers();
    const localeFromHeader =
      headersList.get("x-next-intl-locale") ||
      headersList.get("x-user-language");
    locale = localeFromHeader || routing.defaultLocale;
  }

  try {
    const messages = (await import(`../../messages/${locale}.json`)).default;

    return {
      locale,
      messages,
      timeZone: locale === "vi" ? "Asia/Ho_Chi_Minh" : "UTC",
      now: new Date(),
      // You can also configure other options here
      formats: {
        dateTime: {
          short: {
            day: "numeric",
            month: "short",
            year: "numeric",
          },
        },
      },
    };
  } catch (error) {
    console.error(`Error loading messages for locale ${locale}:`, error);

    // Fallback to default locale messages
    const fallbackMessages = (
      await import(`../../messages/${routing.defaultLocale}.json`)
    ).default;

    return {
      locale: routing.defaultLocale,
      messages: fallbackMessages,
      timeZone: "UTC",
      now: new Date(),
    };
  }
});
