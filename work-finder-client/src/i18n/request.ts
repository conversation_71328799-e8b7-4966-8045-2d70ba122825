import { getRequestConfig } from "next-intl/server";
import { headers } from "next/headers";
import { detectServerLanguage } from "@/lib/i18n/server-language-detection";

export default getRequestConfig(async () => {
  // Get language from middleware-set header or detect from other sources
  const headersList = await headers();
  const middlewareLanguage = headersList.get("x-user-language");

  // Detect user's preferred language
  // In a real app, you might get userPreference from a database or session
  const locale = await detectServerLanguage(middlewareLanguage);

  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default,
    timeZone: locale === "vi" ? "Asia/Ho_Chi_Minh" : "UTC",
    now: new Date(),
  };
});
