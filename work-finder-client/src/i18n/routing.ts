import { defineRouting } from 'next-intl/routing';

export const routing = defineRouting({
  // A list of all locales that are supported
  locales: ['en', 'vi'],

  // Used when no locale matches
  defaultLocale: 'en',

  // The prefix for the default locale
  localePrefix: 'as-needed',

  // Pathnames can be internationalized
  pathnames: {
    '/': '/',
    '/about': {
      en: '/about',
      vi: '/gioi-thieu'
    },
    '/jobs': {
      en: '/jobs',
      vi: '/viec-lam'
    },
    '/companies': {
      en: '/companies',
      vi: '/cong-ty'
    },
    '/dashboard': '/dashboard',
    '/login': {
      en: '/login',
      vi: '/dang-nhap'
    },
    '/register': {
      en: '/register',
      vi: '/dang-ky'
    }
  }
});

export type Pathnames = keyof typeof routing.pathnames;
export type Locale = (typeof routing.locales)[number];
